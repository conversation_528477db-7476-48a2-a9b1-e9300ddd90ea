import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/mall_Collection_widget.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/fun_activity_tab_widget.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/meituan_group_item_widget.dart';

import '../../../../common/widgets/delegate/persistent_builder.dart';
import '../../../../widgets/refresh/refresh_container.dart';
import '../../../provider/fun/fun_list_provider.dart';
import '../../../repository/modals/fun/fun_tab_config.dart';

/// 重构后的 FunPage - 使用纯 Riverpod 状态管理
class FunPage extends ConsumerStatefulWidget {
  const FunPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _FunPageState();
}

class _FunPageState extends ConsumerState<FunPage> {
  final GlobalKey _stickyKey = GlobalKey();
  double stickyHeight = 0;

  @override
  void initState() {
    super.initState();
    // 首次布局后测量高度
    WidgetsBinding.instance.addPostFrameCallback((_) => _updateHeight());
  }

  /// 测量粘性头部的实际高度
  /// 这个方法确保我们获取到准确的组件高度，用于 SliverPersistentHeader
  void _updateHeight() {
    final box = _stickyKey.currentContext?.findRenderObject() as RenderBox?;
    if (box != null && mounted) {
      final newHeight = box.size.height;
      // 只有当高度发生变化时才更新状态，避免不必要的重建
      if (newHeight != stickyHeight) {
        setState(() {
          stickyHeight = newHeight;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF4F4F2),
      body: Stack(
        children: [
          // 背景图片
          Positioned(
            child: Image.network(
              'https://alicdn.msmds.cn/APPSHOW/fun_activity_bg.png',
              width: double.infinity,
              height: 370,
              fit: BoxFit.cover,
            ),
          ),
          SafeArea(
            child: Column(
              children: [
                _buildHeader(),
                Expanded(
                  child: _buildDynamicListView(ref),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _emptyWidget() {
    return Container(
      margin: EdgeInsets.only(top: 80.h),
      alignment: Alignment.center,
      child: Text(
        "暂无数据",
        style: TextStyle(
          fontSize: 12.sp,
          color: const Color(0xFF999999),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Image.network(
            "https://alicdn.msmds.cn/APPSHOW/fun_activity_title_new.png",
            width: 131,
          ),
          Row(
            children: [
              Image.network(
                "https://alicdn.msmds.cn/APPSHOW/fun_activity_location.png",
                width: 11,
              ),
              const SizedBox(width: 5),
              const Text(
                "附近",
                style: TextStyle(color: Colors.white),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderComponents() {
    return Column(
      children: [
        // 视频教程组件
        Container(
          margin: const EdgeInsets.only(bottom: 8),
          alignment: Alignment.center,
          child: _buildVideoTutorial(),
        ),
        // 返现平台视图
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 10),
          child: const MallCollectionWidget(),
        ),
      ],
    );
  }

  Widget _buildVideoTutorial() {
    return Stack(
      children: [
        Container(
          margin: const EdgeInsets.all(10),
          alignment: Alignment.center,
          child: Image.network(
            'https://alicdn.msmds.cn/APPSHOW/fun_activity_step.png',
            fit: BoxFit.contain,
          ),
        ),
        Align(
          alignment: Alignment.topCenter,
          child: GestureDetector(
            onTap: () {
              // TODO: 导航到介绍页面
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 3),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Image.network(
                    'https://alicdn.msmds.cn/APPSHOW/fun_activity_play_tag.png',
                    width: 14,
                    height: 14,
                    fit: BoxFit.contain,
                  ),
                  const SizedBox(width: 3),
                  const Text(
                    '视频教程',
                    style: TextStyle(color: Colors.black, fontSize: 11),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建动态列表视图
  Widget _buildDynamicListView(WidgetRef ref) {
    final tabConfigState = ref.watch(manageFunTabConfigProvider);
    final tabSelection = ref.watch(tabSelectionStateProvider);
    return tabConfigState.when(
      data: (tabConfig) {
        if (tabConfig == null) {
          return CustomListView(
            sliverHeader: _buildSliverHeaders(),
            data: const <String>[],
            footerState: LoadState.noMore,
            renderItem: (context, index, item) => const SizedBox.shrink(),
            empty: _buildErrorWidget('配置加载失败'),
          );
        }

        final mainTabIndex = tabSelection.mainTabIndex;
        final childTabIndex = tabSelection.childTabIndex;

        // 验证索引有效性
        if (mainTabIndex >= tabConfig.mainTab.tabs.length ||
            childTabIndex >= tabConfig.childrenTab.tabs.length) {
          return CustomListView(
            sliverHeader: _buildSliverHeaders(),
            data: const <String>[],
            footerState: LoadState.noMore,
            renderItem: (context, index, item) => const SizedBox.shrink(),
            empty: _buildErrorWidget('标签配置错误'),
          );
        }

        final mainTab = tabConfig.mainTab.tabs[mainTabIndex];
        final childTab = tabConfig.childrenTab.tabs[childTabIndex];
        final currentSort = tabConfig.currentSort;

        return _buildListViewByTabType(
          mainTab.code ?? '',
          mainTab,
          childTab,
          currentSort,
        );
      },
      loading: () => CustomListView(
        sliverHeader: _buildSliverHeaders(),
        data: const <String>[],
        footerState: LoadState.loading,
        renderItem: (context, index, item) => const SizedBox.shrink(),
        empty: Container(
          margin: EdgeInsets.only(top: 80.h),
          child: const Center(child: CircularProgressIndicator()),
        ),
      ),
      error: (error, stackTrace) => CustomListView(
        sliverHeader: _buildSliverHeaders(),
        data: const <String>[],
        footerState: LoadState.fail,
        renderItem: (context, index, item) => const SizedBox.shrink(),
        empty: _buildErrorWidgetWithRetry(ref),
      ),
    );
  }

  /// 构建 Sliver Headers
  /// 实现自适应高度的粘性头部效果
  List<Widget> _buildSliverHeaders() {
    final header = FunActivityTabWidget(
      key: _stickyKey,
    );

    return [
      // 头部组件（视频教程 + 返现平台）
      SliverToBoxAdapter(
        child: _buildHeaderComponents(),
      ),

      // 标签页组件 - 使用自适应高度的粘性效果
      // 当 stickyHeight > 0 时，使用 SliverPersistentHeader 实现粘性效果
      // 否则先使用 SliverToBoxAdapter 作为占位符，避免初始渲染时的布局问题
      if (stickyHeight > 0)
        SliverPersistentHeader(
          pinned: true, // 固定在顶部
          delegate: _SliverAppBarDelegate(
            minHeight: stickyHeight,
            maxHeight: stickyHeight,
            child: header,
          ),
        )
      else
        // 占位符：等待测量到实际高度后再替换为粘性头部
        SliverToBoxAdapter(child: header),
    ];
  }

  /// 根据标签类型构建对应的 CustomListView
  Widget _buildListViewByTabType(
    String tabCode,
    FunTabConfig mainTab,
    FunTabConfig childTab,
    FunSortOption? currentSort,
  ) {
    switch (tabCode) {
      case 'meituan_groupBuying':
      case 'meituan_sharpshooter':
        return _buildMeituanListView(mainTab, childTab, currentSort);
      case 'douyin_groupBuying':
        return _buildPlaceholderListView('抖音团购功能开发中...');
      case 'bawangcan_sort':
        return _buildPlaceholderListView('霸王餐功能开发中...');
      default:
        return _buildPlaceholderListView('请选择标签查看内容');
    }
  }

  /// 构建美团列表视图
  Widget _buildMeituanListView(
    FunTabConfig mainTab,
    FunTabConfig childTab,
    FunSortOption? currentSort,
  ) {
    return Consumer(
      builder: (context, ref, child) {
        final meituanProvider = fetchMeituanCouponListProvider(
          mainTab,
          childTab,
          latitude: 39.9042, // TODO: 使用真实定位
          longitude: 116.4074,
          sortOption: currentSort,
        );

        final meituanState = ref.watch(meituanProvider);

        return meituanState.when(
          data: (response) {
            final items = response.data?.list ?? [];
            if (items.isEmpty) {
              return CustomListView(
                sliverHeader: _buildSliverHeaders(),
                data: const <String>[],
                footerState: LoadState.noMore,
                renderItem: (context, index, item) => const SizedBox.shrink(),
                empty: _emptyWidget(),
              );
            }

            return CustomListView(
              sliverHeader: _buildSliverHeaders(),
              data: items,
              footerState: response.data?.hasMore == true
                  ? LoadState.idle
                  : LoadState.noMore,
              renderItem: (context, index, item) {
                return MeituanGroupItemWidget(
                  item: item,
                  index: index,
                  onShareTap: () => _handleMeituanShare(item),
                  onBuyTap: () => _handleMeituanBuy(item),
                );
              },
              empty: _emptyWidget(),
              onLoadMore: response.data?.hasMore == true
                  ? () =>
                      _loadMoreMeituanData(ref, mainTab, childTab, currentSort)
                  : null,
            );
          },
          loading: () => CustomListView(
            sliverHeader: _buildSliverHeaders(),
            data: const <String>[],
            footerState: LoadState.loading,
            renderItem: (context, index, item) => const SizedBox.shrink(),
            empty: Container(
              margin: EdgeInsets.only(top: 80.h),
              child: const Center(child: CircularProgressIndicator()),
            ),
          ),
          error: (error, stackTrace) => CustomListView(
            sliverHeader: _buildSliverHeaders(),
            data: const <String>[],
            footerState: LoadState.fail,
            renderItem: (context, index, item) => const SizedBox.shrink(),
            empty: _buildErrorWidgetWithRetry(ref),
          ),
        );
      },
    );
  }

  /// 构建占位符列表视图
  Widget _buildPlaceholderListView(String message) {
    return CustomListView(
      sliverHeader: _buildSliverHeaders(),
      data: const <String>[],
      footerState: LoadState.noMore,
      renderItem: (context, index, item) => const SizedBox.shrink(),
      empty: _buildPlaceholderContent(message),
    );
  }

  /// 构建错误提示组件
  Widget _buildErrorWidget(String message) {
    return Container(
      margin: EdgeInsets.only(top: 80.h),
      alignment: Alignment.center,
      child: Text(
        message,
        style: TextStyle(
          fontSize: 14.sp,
          color: const Color(0xFF999999),
        ),
      ),
    );
  }

  /// 构建带重试按钮的错误组件
  Widget _buildErrorWidgetWithRetry(WidgetRef ref) {
    return Container(
      margin: EdgeInsets.only(top: 80.h),
      alignment: Alignment.center,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '加载失败',
            style: TextStyle(
              fontSize: 14.sp,
              color: const Color(0xFF999999),
            ),
          ),
          SizedBox(height: 8.h),
          TextButton(
            onPressed: () => ref.invalidate(manageFunTabConfigProvider),
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  /// 加载更多美团数据
  Future<void> _loadMoreMeituanData(
    WidgetRef ref,
    FunTabConfig mainTab,
    FunTabConfig childTab,
    FunSortOption? currentSort,
  ) async {
    // TODO: 实现分页加载逻辑
    // 这里需要扩展 fetchMeituanCouponListProvider 支持分页
    debugPrint('加载更多美团数据');
  }

  /// 处理美团分享
  void _handleMeituanShare(dynamic item) {
    // TODO: 实现分享逻辑
    debugPrint('分享美团商品: ${item.goodsName}');
  }

  /// 处理美团购买
  void _handleMeituanBuy(dynamic item) {
    // TODO: 实现购买逻辑
    debugPrint('购买美团商品: ${item.goodsName}');
  }

  Widget _buildPlaceholderContent(String text) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Center(
        child: Text(
          text,
          style: const TextStyle(fontSize: 16, color: Colors.grey),
        ),
      ),
    );
  }
}

/// 自定义 SliverPersistentHeaderDelegate
/// 用于实现自适应高度的粘性头部效果
/// 与 my_test_widget.dart 中的实现保持一致
class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final double minHeight;
  final double maxHeight;
  final Widget child;

  _SliverAppBarDelegate({
    required this.minHeight,
    required this.maxHeight,
    required this.child,
  });

  @override
  double get minExtent => minHeight;

  @override
  double get maxExtent => maxHeight;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    // 使用 SizedBox.expand 确保子组件填充整个可用空间
    return SizedBox.expand(child: child);
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    // 当任何关键属性发生变化时重建
    return minHeight != oldDelegate.minHeight ||
        maxHeight != oldDelegate.maxHeight ||
        child != oldDelegate.child;
  }
}
