import 'package:flutter/material.dart';
import 'package:msmds_platform/widgets/refresh/refresh_container.dart';


class StickyPage extends StatefulWidget {
  const StickyPage({super.key});

  @override
  State<StickyPage> createState() => _StickyPageState();
}

class _StickyPageState extends State<StickyPage> {
  final GlobalKey _headerKey = GlobalKey();
  double _headerHeight = 0;

  @override
  void initState() {
    super.initState();
    // 首次布局后测量高度
    WidgetsBinding.instance.addPostFrameCallback((_) => _updateHeight());
  }

  void _updateHeight() {
    final box = _headerKey.currentContext?.findRenderObject() as RenderBox?;
    if (box != null && mounted) {
      setState(() {
        _headerHeight = box.size.height;
      });
    }
  }

  @override
  Widget build(BuildContext context) {

    return Scaffold(
      body: CustomListView(
        sliverHeader: _buildSliverHeaders(),
        data: const <String>["1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1","1"],
        footerState: LoadState.noMore,
        renderItem: (context, index, item) => Container(
          child: Text("商品 $index"),
       ),
        empty: Container(
          alignment: Alignment.center,
          margin: EdgeInsets.only(top: 80),
          child: const Text("暂无数据"),
        ),
      ),
    );
  }
    /// 构建 Sliver Headers
  /// 实现自适应高度的粘性头部效果
  List<Widget> _buildSliverHeaders() {
    final header = HeaderContent(key: _headerKey);

    return [
      // 头部组件（视频教程 + 返现平台）
       // 顶部图片
          SliverToBoxAdapter(
            child: Container(
              color: Colors.blue,
              height: 200,
            ),
          ),
          // 标题栏
          if (_headerHeight > 0)
            SliverPersistentHeader(
              pinned: true,
              delegate: _SliverAppBarDelegate(
                minHeight: _headerHeight,
                maxHeight: _headerHeight,
                child: header,
              ),
            )
          else
            // 先占位，等测量到高度后再替换
            SliverToBoxAdapter(child: header),
    ];
  }
}

/// 黄色+红色区域
class HeaderContent extends StatelessWidget {
  const HeaderContent({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.yellow,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 黄色部分
          Column(
            children: [
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: const [
                  Text("外卖霸王餐"),
                  Text("神抢手"),
                  Text("美团团购"),
                  Text("抖音团购"),
                ],
              ),
              Container(
                margin: const EdgeInsets.all(8),
                padding: const EdgeInsets.symmetric(horizontal: 8),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                ),
                height: 36,
                child: Row(
                  children: const [
                    Icon(Icons.search),
                    SizedBox(width: 5),
                    Text("搜索更多优惠，下单享返现"),
                  ],
                ),
              ),
            ],
          ),

          // 红色部分 Wrap
          Container(
            color: Colors.red,
            padding: const EdgeInsets.all(8),
            child: Wrap(
              spacing: 8,
              runSpacing: 8,
              children: List.generate(
                12,
                (index) => Chip(label: Text("分类 $index")),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Delegate
class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final double minHeight;
  final double maxHeight;
  final Widget child;

  _SliverAppBarDelegate({
    required this.minHeight,
    required this.maxHeight,
    required this.child,
  });

  @override
  double get minExtent => minHeight;

  @override
  double get maxExtent => maxHeight;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(child: child);
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return minHeight != oldDelegate.minHeight ||
        maxHeight != oldDelegate.maxHeight ||
        child != oldDelegate.child;
  }
}
