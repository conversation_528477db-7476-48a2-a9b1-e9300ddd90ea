import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:app_links/app_links.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/config/config_provider.dart';
import 'package:msmds_platform/app/provider/home/<USER>';
import 'package:msmds_platform/app/lifecycle/app_lifecycle_observer.dart';
import 'package:msmds_platform/app/repository/modals/config/icon_config.dart';
import 'package:msmds_platform/app/view/home/<USER>/main_page.dart';

// import 'package:msmds_platform/app/view/home/<USER>/mall_page.dart';
import 'package:msmds_platform/app/view/home/<USER>/me_page.dart';

import 'package:msmds_platform/app/view/home/<USER>/sign_page.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';
import 'package:msmds_platform/common/widgets/tab/tab_image_widget.dart';
import 'package:msmds_platform/config/global_config.dart';
import 'package:msmds_platform/widgets/keepalive/keep_alive_wrapper.dart';

import '../../../plugin/alibc/alibc_ohos_plugin.dart';
import '../../../plugin/wechat/src/fluwx.dart';
import 'fun/fun_page.dart';
import 'fun/widgets/my_test_widget.dart';

/// Copyright (C), 2021-2023, Franky Lee
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: home_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/8 15:39
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/8 15:39
/// @UpdateRemark: 更新说明

class HomePage extends ConsumerStatefulWidget {
  const HomePage({super.key, this.arguments});

  final Object? arguments;

  @override
  HomePageState createState() => HomePageState();
}

class HomePageState extends ConsumerState<HomePage> {
  final NeverScrollableScrollPhysics _scrollPhysics =
      const NeverScrollableScrollPhysics();

  late WidgetsBindingObserver appLifecycleObserver;

  /// deep link设置
  final AppLinks _appLinks = AppLinks();
  StreamSubscription<Uri>? _linkSubscription;

  final _alibcFlutterPlugin = AlibcFlutterPlugin();

  @override
  void initState() {
    super.initState();

    /// 阿里百川SDK初始化
    initAlibcSdk();

    /// 微信API注册
    registerWxApi();

    /// 注册app生命周期
    appLifecycleObserver = AppLifecycleObserver(ref: ref);
    WidgetsBinding.instance.addObserver(appLifecycleObserver);

    WidgetsBinding.instance.addPostFrameCallback(
      (timeStamp) {
        /// 更新用户最新信息
        ref.read(newestUserInfoProvider.notifier).getNewestUserInfo();

        /// deep link初始化
        initDeepLinks();

        /// 打开app时执行粘贴板识别
        if (GlobalConfig.account != null) {
          ref.read(convertContentProvider.notifier).convert();
        }

        /// 点击开屏广告进入首页
        adClickAction();
      },
    );
  }

  /// 阿里百川SDK初始化
  Future<void> initAlibcSdk() async {
    try {
      if (Platform.isAndroid){
        return;
      }
      _alibcFlutterPlugin.initAlibc("3.6.5");
    } on PlatformException {
      debugPrint("alibc init fail.");
    }
  }

  /// 注册微信API
  void registerWxApi() {
      if (Platform.isAndroid){
        return;
      }
    Fluwx fluwx = Fluwx();
    fluwx.registerApi(appId: "wx16f8d3a14a344cfa");
  }

  /// 初始化deep link
  void initDeepLinks() async {
    // Check initial link if app was in cold state (terminated)
    final appLink = await _appLinks.getInitialAppLink();
    if (appLink != null && mounted) {
      ref.read(homeProvider.notifier).openAppLink(context, appLink);
    }

    // Handle link when app is in warm state (front or background)
    _linkSubscription = _appLinks.uriLinkStream.listen((uri) {
      ref.read(homeProvider.notifier).openAppLink(context, uri);
    });
  }

  /// 点击开屏广告进入首页
  void adClickAction() {
    Future.delayed(const Duration(seconds: 1), () {
      try {
        if (widget.arguments == null) return;
        var adConfigData = (widget.arguments as Map)["adConfigData"] as String;
        if (adConfigData.isNotEmpty) {
          debugPrint('adClickAction: $adConfigData');
          var typeData = jsonDecode(adConfigData);
          var jumpUrl = typeData["url"];
          ref.read(configItemClickProvider.notifier).configItemClick(
                context,
                IconConfig()
                  ..jumpUrl = jumpUrl
                  ..typeData = adConfigData,
              );
        }
      } catch (e) {
        debugPrint("adClickAction-e: $e");
      }
    });
  }

  @override
  void dispose() {
    _linkSubscription?.cancel();
    WidgetsBinding.instance.removeObserver(appLifecycleObserver);
    super.dispose();
  }

  /// PageView
  Widget _buildPageView() {
    return Consumer(
      builder: (context, ref, child) {
        return PageView(
          controller: ref.watch(
            homeProvider.select((value) => value.pageController),
          ),
          physics: _scrollPhysics,
          children: const [
            KeepAliveWrapper(child: MainPage()),
            // KeepAliveWrapper(child: MallPage()),
            KeepAliveWrapper(child: SignPage()),
            KeepAliveWrapper(child: FunPage()),
            KeepAliveWrapper(child: MePage()),
          ],
        );
      },
    );
  }

  /// bottom navigation bar
  Widget _buildBottomNavigationBar() {
    return Consumer(
      builder: (context, ref, child) {
        return BottomNavigationBar(
          currentIndex: ref.watch(
            homeProvider.select((value) => value.index),
          ),
          type: BottomNavigationBarType.fixed,
          items: [
            BottomNavigationBarItem(
              icon: TabImageWidget(icon: tabBarMain, size: 22.r),
              activeIcon: TabImageWidget(icon: tabBarMainActive, size: 22.r),
              label: "首页",
            ),
            // BottomNavigationBarItem(
            //   icon: TabImageWidget(icon: mallIcon, size: 22.r),
            //   activeIcon: TabImageWidget(icon: mallIconActive, size: 22.r),
            //   label: "商城返利",
            // ),
            BottomNavigationBarItem(
              icon: TabImageWidget(icon: tabBarSign, size: 22.r),
              activeIcon: TabImageWidget(icon: tabBarSignActive, size: 22.r),
              label: "签到赚钱",
            ),
            BottomNavigationBarItem(
              icon: TabImageWidget(icon: tabBarFun, size: 22.r),
              activeIcon: TabImageWidget(icon: tabBarFunActive, size: 22.r),
              label: "吃喝玩乐",
            ),
            BottomNavigationBarItem(
              icon: TabImageWidget(icon: tabBarMe, size: 22.r),
              activeIcon: TabImageWidget(icon: tabBarMeActive, size: 22.r),
              label: "我的",
            ),
          ],
          onTap: (index) {
            if (ref.exists(homeProvider)) {
              ref.read(homeProvider.notifier).jumpToPage(index);
            } else {
              ref.watch(homeProvider.notifier).jumpToPage(index);
            }
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    GlobalConfig.localeInfo = Localizations.localeOf(context);
    return Scaffold(
      body: _buildPageView(),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }
}
